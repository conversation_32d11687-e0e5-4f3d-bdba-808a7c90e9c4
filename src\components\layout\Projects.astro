---
import ProjectCard from "../common/ProjectCard.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n } = Astro.props;
---

<section class="projects">
  <h2 class="title no-select">{i18n.projects.title}</h2>
  <div class="container">
    <ProjectCard />
  </div>
</section>

<style>
  .projects {
    padding: var(--spacing-5) var(--spacing-4);
    background: var(--color-background);
    text-align: center;
  }
  .container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
  }

  .title {
    color: var(--color-text-primary);
    margin: 0;
  }
  @media (max-width: 900px) {
    .container {
      flex-direction: column;
    }
  }
  @media (max-width: 600px) {
    .container {
      gap: var(--spacing-3);
    }
  }
  @media (max-width: 400px) {
    .container {
      gap: var(--spacing-2);
    }
  }
</style>
