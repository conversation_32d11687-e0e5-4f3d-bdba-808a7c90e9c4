---
export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n } = Astro.props;
---

<section class="contact">
  <h2 class="title no-select">{i18n.contact.title}</h2>
</section>

<style>
  .contact {
    padding: var(--spacing-5) var(--spacing-4);
    background: var(--color-background);
    text-align: center;
  }

  .title {
    color: var(--color-text-primary);
    margin: 0;
  }
</style>
