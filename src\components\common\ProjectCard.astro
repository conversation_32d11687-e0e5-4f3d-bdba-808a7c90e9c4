---
import { Image } from "astro:assets";
import Icon from "./Icon.astro";
---

<div class="project-card">
  <div class="image-container">
    <Image
      src={image}
      alt={title}
      widths={[240, 320, 400]}
      sizes="(max-width: 900px) 70vw, 380px"
      loading="lazy"
      decoding="async"
    />
  </div>
  <div class="content">
    <h3 class="title no-select">{title}</h3>
    <p class="description no-select">{description}</p>
    <a
      class="btn btn--primary"
      href={link}
      rel="noopener noreferrer"
      target="_blank"
    >
      {i18n.projects.view_project}
    </a>
  </div>
</div>

<style>
  .project-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    width: 100%;
    max-width: 300px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }

  .project-card:hover {
    transform: scale(1.02);
  }

  .image-container {
    overflow: hidden;
  }

  .image-container img {
    display: block;
    width: 100%;
    height: auto;
  }

  .content {
    padding: var(--spacing-4);
  }

  .title {
    color: var(--color-text-primary);
    margin: 0;
  }

  .description {
    color: var(--color-text-secondary);
    margin: 0;
  }
</style>
