---
import QuickAccess from "../common/QuickAccess.astro";
import ThemeToggle from "../common/ThemeToggle.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n, lang } = Astro.props;
---

<header>
  <div class="navBar">
    <div class="left-actions">
      <img
        class="logo"
        src="/images/logo-jimmy.svg"
        alt="Logo de <PERSON>"
      />
      <div class="actions">
        <div class="button-about">
          <a href="#about">{i18n.about.title}</a>
        </div>
        <div class="button-projects">
          <a href="#projects">{i18n.projects.title}</a>
        </div>
        <div class="button-contact">
          <a href="#contact">{i18n.hero.cta_contact}</a>
        </div>
      </div>
      <ThemeToggle />
    </div>
    <QuickAccess
      label={i18n.common.quick_access}
      keycapLabel={i18n.common.keycap_label}
    />
  </div>
</header>

<style>
  :root {
    --header-height: 80px;
    --glass-radius: 16px;
    --glass-blur: 16px;
    --glass-saturate: 160%;
  }

  /* Header: positionné en haut, centré */
  header {
    position: fixed;
    top: 0;
    z-index: 40;
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-top: var(--spacing-5);
    pointer-events: none;
  }

  /* Plaque “liquid glass” */
  .navBar {
    pointer-events: auto;
    position: relative;
    height: var(--header-height);
    width: min(1100px, 90%);
    padding: 0 var(--spacing-4);

    display: flex;
    justify-content: space-between;
    align-items: center;

    border-radius: var(--glass-radius);
    border: 1px solid rgba(255, 255, 255, 0.18);
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.2),
      rgba(255, 255, 255, 0.1)
    );
    backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturate));
    -webkit-backdrop-filter: blur(var(--glass-blur))
      saturate(var(--glass-saturate));
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
    overflow: hidden;
  }

  .navBar::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.35),
      transparent 45%
    );
    mix-blend-mode: screen;
    pointer-events: none;
  }

  .navBar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.22);
    pointer-events: none;
  }

  .left-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  .actions {
    display: flex;
    gap: var(--spacing-4);
  }

  @supports not (
    (backdrop-filter: blur(1px)) or (-webkit-backdrop-filter: blur(1px))
  ) {
    .navBar {
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.16);
    }
    .navBar::before {
      display: none;
    }
  }

  @media (prefers-color-scheme: dark) {
    .navBar {
      background: linear-gradient(
        to bottom,
        color-mix(in oklab, var(--gray-800) 20%, transparent),
        color-mix(in oklab, var(--gray-50) 10%, transparent)
      );
      border-color: rgba(255, 255, 255, 0.14);
      box-shadow:
        0 12px 34px rgba(0, 0, 0, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.18);
    }
  }

  .navBar:hover {
    box-shadow:
      0 14px 36px rgba(0, 0, 0, 0.28),
      inset 0 1px 0 rgba(255, 255, 255, 0.28);
  }

  .logo {
    width: 40px;
    height: 40px;
  }
</style>
