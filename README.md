# Portfolio de <PERSON> 👋

![jimmy-portfolio](https://count.getloli.com/@jimmy-portfolio?name=jimmy-portfolio&theme=miku&padding=7&offset=0&align=top&scale=1&pixelated=1&darkmode=0)

Ce dépôt contient le code source de mon portfolio personnel, développé avec le framework **Astro**. Il met en avant mes compétences et mes projets en tant que développeur spécialisé en développement mobile (**Unity**), web et **réalité augmentée**.

➡️ **Lien vers le site** : [Lien vers votre site déployé à ajouter ici]

---

## ✨ Caractéristiques

- **Performances Optimales** : Construit avec Astro pour un site statique ultra-rapide.
- **Design System Complet** : Utilisation d'un système de design modulaire en CSS pur avec des variables pour une maintenance et une cohérence facilitées.
- **Thème Clair & Sombre** : Le site s'adapte automatiquement au thème du système d'exploitation de l'utilisateur (`prefers-color-scheme`).
- **Responsive Design** : Une typographie et des espacements qui s'ajustent pour une expérience utilisateur parfaite sur tous les appareils, du mobile au bureau.

---

## 🛠️ Tech Stack

- **Framework** : Astro
- **Langages** : HTML, CSS, TypeScript
- **Déploiement** : [À compléter : Vercel, Netlify, GitHub Pages, etc.]

---

## 📂 Structure du Projet

Le projet suit la structure standard d'Astro pour une organisation claire et logique.

```

/
├── public/
│ └── ... (fichiers statiques comme le favicon)
└── src/
├── assets/
│ └── ... (images, SVGs optimisés)
├── components/
│ └── ... (composants Astro réutilisables, ex: Header.astro)
├── layouts/
│ └── Layout.astro (structure de base des pages)
├── pages/
│ └── index.astro (la page d'accueil)
└── styles/
├── font.css (définition des polices et tailles)
└── global.css (système de design : variables, thèmes, styles globaux)

```

---

## 🚀 Démarrage Rapide

Pour lancer ce projet en local, suivez ces étapes :

1. **Cloner le dépôt**

```bash
git clone https://github.com/[VOTRE-NOM-UTILISATEUR]/JIMMY-PORTFOLIO.git
```

2. **Naviguer dans le dossier du projet**

```bash
cd JIMMY-PORTFOLIO
```

3. **Installer les dépendances**

```bash
npm install
```

4. **Lancer le serveur de développement**

```bash
npm run dev
```

Votre site est maintenant accessible à l'adresse `http://localhost:4321`.

---

## ⚙️ Scripts Disponibles

- `npm run dev` : Lance le serveur de développement avec le rechargement à chaud (HMR).
- `npm run build` : Compile et optimise le site pour la production dans le dossier `dist/`.
- `npm run preview` : Lance un serveur local pour prévisualiser le build de production.

---

## 📄 Licence

Ce projet est sous licence **MIT**.
